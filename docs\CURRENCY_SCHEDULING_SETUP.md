# Currency Exchange Rate Scheduling Setup

This guide explains how to set up automatic currency rate updates directly in Supabase without external dependencies.

## Option 1: Supabase pg_cron (Recommended)

### Step 1: Enable pg_cron Extension
1. Go to your Supabase Dashboard
2. Navigate to **Database** → **Extensions**
3. Search for `pg_cron` and enable it
4. This requires the Pro plan or higher

### Step 2: Schedule the Currency Update
Run this SQL in your Supabase SQL Editor:

```sql
-- Schedule currency updates every 6 hours
SELECT cron.schedule(
  'currency-rate-update',           -- job name
  '0 */6 * * *',                   -- every 6 hours
  'SELECT public.trigger_currency_update_webhook();'
);
```

### Step 3: Verify the Schedule
Check if the job is scheduled:

```sql
SELECT * FROM cron.job WHERE jobname = 'currency-rate-update';
```

## Option 2: External Cron Service (Easier Setup)

If you don't have pg_cron available, use an external service like [cron-job.org](https://cron-job.org):

### Step 1: Get Your Edge Function URL
Your URL will be: `https://mbyiidayuburouqozgfq.supabase.co/functions/v1/scheduled-currency-update`

### Step 2: Set Up External Cron
1. Go to [cron-job.org](https://cron-job.org) or similar service
2. Create a new cron job with:
   - **URL**: `https://mbyiidayuburouqozgfq.supabase.co/functions/v1/scheduled-currency-update`
   - **Method**: POST
   - **Schedule**: Every 6 hours (`0 */6 * * *`)
   - **Headers**: 
     ```
     Authorization: Bearer YOUR_SUPABASE_ANON_KEY
     Content-Type: application/json
     ```

### Step 3: Optional Security
Add a secret token to your Supabase Edge Function environment variables:
- Variable: `CRON_SECRET_TOKEN`
- Value: A random secure string

Then include this in your cron job headers:
```
Authorization: Bearer your_secret_token
```

## Option 3: Supabase Database Webhooks

### Step 1: Create a Database Webhook
1. Go to **Database** → **Webhooks** in Supabase Dashboard
2. Create a new webhook:
   - **Table**: `currency_update_jobs`
   - **Events**: INSERT
   - **Type**: HTTP Request
   - **URL**: `https://mbyiidayuburouqozgfq.supabase.co/functions/v1/update-currency-rates`
   - **Method**: POST

### Step 2: Trigger Updates
The webhook will automatically call the Edge Function whenever a new job is inserted.

## Monitoring

### Check Update Status
```sql
SELECT * FROM public.currency_update_status;
```

### View Recent Jobs
```sql
SELECT * FROM public.currency_update_jobs 
ORDER BY created_at DESC 
LIMIT 10;
```

### Manual Trigger (for testing)
```sql
SELECT public.trigger_currency_update_webhook();
```

## Environment Variables Required

Make sure these are set in your Supabase Edge Functions:

- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Your service role key
- `FOREX_API_KEY`: Your SilverLining API key
- `CRON_SECRET_TOKEN`: (Optional) Security token for external cron

## Troubleshooting

### Check if pg_cron is enabled:
```sql
SELECT * FROM pg_extension WHERE extname = 'pg_cron';
```

### View cron job logs:
```sql
SELECT * FROM cron.job_run_details 
WHERE jobname = 'currency-rate-update' 
ORDER BY start_time DESC;
```

### Unschedule a job:
```sql
SELECT cron.unschedule('currency-rate-update');
```

## Benefits of This Approach

✅ **No External Dependencies**: Everything runs within Supabase
✅ **Secure**: API keys never exposed to frontend
✅ **Reliable**: Supabase infrastructure handles scheduling
✅ **Monitorable**: Built-in logging and status tracking
✅ **Cost-Effective**: No additional services required
✅ **Scalable**: Handles multiple concurrent requests efficiently
